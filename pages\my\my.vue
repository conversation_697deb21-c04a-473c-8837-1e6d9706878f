<template>
	<view>
		<view class="bg" :style="{'background-image': 'url('+imgUrl+'234.png'+')'}">
			<view
				:style="{'height':''+heightSystemss+'px','top':''+statusBarHeightss+'px','lineHeight':''+heightSystemss+'px','left':''+10+'px'}"
				class="iconDizhssi">
			</view>
			<view style="height: 180rpx;"></view>
			<view class="display-a margin-bottom_30rpx" v-if="user.id" style="padding: 0 26rpx;" @click="getAuth(2)">
				<image class="img-30" :src="user.is_member == 1 ? imgUrl + '160.png' : imgUrl + '159.png'"></image>
				<view class="color_FFFFFF">
					<view class="margin-bottom_10rpx display-a">
						<view class="font-weight_bold font-size_36rpx">{{user.nickname}}</view>
						<image v-if="user.is_member == 1" class="img-31" :src="imgUrl + '31.png'"></image>
					</view>
					<view class="display-a">
						<image class="img-329" :src="imgUrl+'329.png'"></image>
						<view style="color: #CAFF00;">我的点数: {{user.balance?user.balance:'0'}}</view>
					</view>
				</view>
			</view>
			<view class="display-a margin-bottom_50rpx" v-else style="padding: 0 26rpx;" @click="getAuth(1)">
				<image class="img-30" :src="imgUrl + '159.png'"></image>
				<view class="color_FFFFFF">
					<view class="margin-bottom_10rpx font-weight_bold">游客模式</view>
					<view class="font-size_26rpx">请点击登录</view>
				</view>
			</view>
			<view class="display-fw-a" v-if="user.id">
				<!-- <view class="color_FFFFFF" :class="cloneSet.voice_high_open == 1 ? 'width_250rpx-center' : 'width_374rpx-center'">
					<view class="font-size_36rpx font-weight_bold">{{user.balance?user.balance:'--'}}</view>
					<view class="font-size_26rpx">我的点数</view>
				</view> -->
				<!-- <view class="color_FFFFFF width_250rpx-center margin-bottom_20rpx">
					<view class="font-size_36rpx font-weight_bold">
						{{user.ai_copywriting_times?user.ai_copywriting_times:'0'}}
					</view>
					<view class="font-size_26rpx">AI文案(次)</view>
				</view> -->
				<view class="color_FFFFFF width_250rpx-center margin-bottom_20rpx">
					<view class="font-size_36rpx font-weight_bold">
						{{user.second_infinite==1?'无限':user.second?(user.second/60).toFixed(2):'0'}}
					</view>
					<view class="font-size_26rpx">合成视频(分)</view>
				</view>
				<view class="width_250rpx-center color_FFFFFF margin-bottom_20rpx" v-if="cloneSet.voice_high_open == 1">
					<view class="font-size_36rpx font-weight_bold">{{user.voice_twin_count?user.voice_twin_count:'0'}}
					</view>
					<view class="font-size_26rpx">高保真声音(次)</view>
				</view>
				<view class="width_250rpx-center color_FFFFFF margin-bottom_20rpx" v-if="cloneSet.voice_high_open == 1">
					<view class="font-size_36rpx font-weight_bold">
						{{user.high_fidelity_words_number?user.high_fidelity_words_number:'0'}}
					</view>
					<view class="font-size_26rpx">高保真合成(字)</view>
				</view>
				<view class="width_250rpx-center color_FFFFFF margin-bottom_20rpx"
					v-if="cloneSet.xunfei_sound_clone_swich == 1">
					<view class="font-size_36rpx font-weight_bold">
						{{user.xunfei_sound_clone_words_number?user.xunfei_sound_clone_words_number:'0'}}
					</view>
					<view class="font-size_26rpx">专业版声音(次)</view>
				</view>
				<view class="width_250rpx-center color_FFFFFF margin-bottom_20rpx"
					v-if="cloneSet.xunfei_sound_clone_swich == 1">
					<view class="font-size_36rpx font-weight_bold">
						{{user.xunfei_sound_generate_words_number?user.xunfei_sound_generate_words_number:'0'}}
					</view>
					<view class="font-size_26rpx">专业版合成(字)</view>
				</view>
			</view>
		</view>

		<view class="member-bg" @click="getMember()" v-if="memberSet.is_open == 1"
			:style="{'background-image': 'url('+imgUrl+'235.png'+')'}">
			<view class="display-a">
				<view>
					<view class="display-a margin-bottom_30rpx">
						<image class="img-13" :src="imgUrl + '13.png'"></image>
						<view class="color_874F03">开通VIP专属特权</view>
					</view>
					<view v-if="user.is_member == 1" class="font-size_26rpx color_515151">到期时间: {{user.maturity_time}}
					</view>
					<view v-else class="m-tips">海量会员权益等您来体验</view>
				</view>
				<view class="renew">立即开通</view>
			</view>
		</view>
		<view class="list-public" style="padding: 30rpx 0; margin-top: 30rpx;" v-if="user.id">
			<view class="display-a padding_0_20rpx">
				<view class="m-line"></view>
				<view class="color_FFFFFF font-size_32rpx font-weight_bold">个人信息</view>

				<view class="display-a margin-left-auto" @click="goUserInfo()">
					<view class="color_c7c7c7 font-size_26rpx">点击查看</view>
					<image class="img-242" :src="imgUrl+'242.png'"></image>
				</view>
			</view>
		</view>
		<view class="img-257 color_FFFFFF" :style="{'background-image': 'url('+imgUrl+'257.png'+')'}" v-if="false">
			<view class="display-a margin-bottom_30rpx">
				<view class="m-line"></view>
				<view class="font-size_32rpx font-weight_bold">我的收益</view>
				<view class="display-a margin-left-auto" @click="getWithdrawalRecord()">
					<view class="color_c7c7c7 font-size_26rpx">查看明细</view>
					<image class="img-242" :src="imgUrl+'242.png'"></image>
				</view>
			</view>

			<view class="display-a padding-bottom_20rpx margin-bottom_30rpx"
				style="border-bottom: 1px solid rgba(255, 255, 255, 0.11);">
				<view class="font-size_40rpx font-weight_bold">￥{{brokerageUser.brokerage}}</view>
				<view class="withdrawal" v-if="cash_open == 1" @click="getWithdrawal()">立即提现</view>
			</view>

			<view class="display-a">
				<view class="width_236rpx-center">
					<view class="color_969696">今日收益(元)</view>
					<view class="font-weight_bold">{{Number(brokerageUser.todayGet).toFixed(2)}}</view>
				</view>
				<view class="width_236rpx-center">
					<view class="color_969696">本月收益(元)</view>
					<view class="font-weight_bold">{{Number(brokerageUser.monthGet).toFixed(2)}}</view>
				</view>
				<view class="width_236rpx-center">
					<view class="color_969696">累计收益(元)</view>
					<view class="font-weight_bold">{{Number(brokerageUser.allGet).toFixed(2)}}</view>
				</view>
			</view>

		</view>


		<view class="list-public" style="padding: 30rpx 0 0;" v-if="user.id">
			<view class="display-a margin-bottom_40rpx padding_0_20rpx">
				<view class="m-line"></view>
				<view class="color_FFFFFF font-size_32rpx font-weight_bold">更多功能</view>
			</view>
			<view class="display-fw-a">
				<view class="width_166rpx-center" @click="getAssets()" v-if="configSet.resource_open == 1">
					<image class="img-14" :src="imgUrl + '256.png'"></image>
					<view class="color_FFFFFF">资产订单</view>
				</view>
				<view class="width_166rpx-center" v-if="partner_is_open == 1" @click="getPartner()">
					<image class="img-14" :src="imgUrl + '16.png'"></image>
					<view class="color_FFFFFF">合伙人</view>
				</view>
				<view class="width_166rpx-center" @click="getShareholder()"
					v-if="is_open == 1 && is_shareholder_swich == 1">
					<image class="img-14" :src="imgUrl + '327.png'"></image>
					<view class="color_FFFFFF">股东分红</view>
				</view>
				<view class="width_166rpx-center" @click="getConsumption()">
					<image class="img-14" :src="imgUrl + '17.png'"></image>
					<view class="color_FFFFFF">账户明细</view>
				</view>
				<view class="width_166rpx-center" @click="getStatistics()"
					v-if="kfSet.clip_swtich == 1 && repostSetIsOpen == 1">
					<image class="img-14" :src="imgUrl + '188.png'"></image>
					<view class="color_FFFFFF">数据统计</view>
				</view>
				<view class="width_166rpx-center" @click="getDistribution()" v-if="is_open == 1">
					<image class="img-14" :src="imgUrl + '18.png'"></image>
					<view class="color_FFFFFF">分销中心</view>
				</view>
				<view class="width_166rpx-center" @click="getCourse()" v-if="kfSet.help_switch == 1">
					<image class="img-14" :src="imgUrl + '237.png'"></image>
					<view class="color_FFFFFF">帮助教程</view>
				</view>
				<view class="width_166rpx-center" @click="getContactUs()">
					<image class="img-14" :src="imgUrl + '236.png'"></image>
					<view class="color_FFFFFF">关于我们</view>
				</view>
				<view class="width_166rpx-center">
					<button open-type="contact">
						<image class="img-14" :src="imgUrl + '20.png'"></image>
						<view class="color_FFFFFF" style="margin-top: -40rpx;">联系客服</view>
					</button>
				</view>
			</view>
		</view>
		<view class="h_20rpx"></view>
		<block v-if="systems.banner_id">
			<view style="width: 710rpx;height: 240rpx;margin-left: 20rpx;border-radius: 10rpx;">
				<ad v-if="systems.banner_id" :unit-id="systems.banner_id"></ad>
			</view>
			<view class="h_20rpx"></view>
		</block>
		<template v-if="user.id">
			<button class="button-phone" @click="logOut">退出登录</button>
			<view class="h_20rpx"></view>
		</template>

		<sunui-tabbar :fixed="true" :current="tabIndex" :types="1"></sunui-tabbar>

	</view>
</template>

<script>
	export default {
		data() {
			return {

				tabIndex: '',

				heightSystemss: '',
				statusBarHeightss: '',

				imgUrl: this.$imgUrl,

				user: {},

				systems: {},

				is_open: '', //分销 1开启
				cash_open: '', //是否开启提现 1开启

				repostSetIsOpen: '', //转发设置 1开启

				partner_is_open: '', //合伙人 1开启

				memberSet: {},

				cloneSet: {},

				brokerageUser: {},

				configSet: {}, //资产设置

				kfSet: {},

				is_shareholder_swich: '', //股东分红 1开启

			}
		},

		onLoad(options) {
			if (options.index) {
				this.tabIndex = options.index;
			}
			this.getSystemInfo();
			this.getMemberSet();
			this.getRepostSet();
			this.getCloneSet();
			this.indexkfSet();
			this.getDistributionLevel();
			this.getReleaseConfig();
		},

		onShow() {
			this.getCustomerConfig();
			this.userInfo();
			this.getBrokerageSet();
			this.getPartnerSet();
			this.getSystem();
			this.getBrokerageIndex();
			this.getIndexWay();
		},

		methods: {

			logOut() {
				uni.showModal({
					content: "确认退出登录?",
					confirmText: "确认",
					cancelText: "取消",
					success: (res) => {
						if (res.confirm) {
							uni.clearStorage()
							uni.redirectTo({
								url: '/pages/auth/auth?type=1'
							})
						} else if (res.cancel) {

						}
					}
				})
			},

			//线路自定义名称
			async getIndexWay() {
				const result = await this.$http.post({
					url: this.$api.indexWay
				});
				if (result.errno == 0) {
					uni.setStorageSync('indexWay', result.data);
				}
			},

			//资产配置
			async getReleaseConfig() {
				const result = await this.$http.post({
					url: this.$api.releaseConfig
				});
				if (result.errno == 0) {
					this.configSet = result.data;

				}
			},

			//股东分红
			getShareholder() {

				if (this.user.is_shareholder == 1) {
					uni.navigateTo({
						url: '/pages/my/distribution/shareholder'
					})
				} else {
					uni.showModal({
						content: "请先成为股东,点击确认查看怎么成为股东!",
						confirmText: "确认",
						cancelText: "关闭",
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/my/distribution/moneyThat'
								})
							} else if (res.cancel) {

							}
						}
					})
				}
			},

			//分销等级
			async getDistributionLevel() {
				const resolu = await this.$http.get({
					url: this.$api.distributionLevel
				})
				if (resolu.errno == 0) {
					this.levelObj = resolu.data;
				}
			},

			// 客服设置接口
			async indexkfSet() {
				const result = await this.$http.get({
					url: this.$api.kfSet
				})
				if (result.errno == 0) {
					this.kfSet = result.data;
				}
			},

			//客服配置
			async getCustomerConfig() {
				const result = await this.$http.post({
					url: this.$api.customerConfig
				});
				if (result.errno == 0) {
					uni.setStorageSync('customerConfig', result.data);
				}
			},

			//跳转个人信息
			goUserInfo() {
				uni.navigateTo({
					url: '/subPackages/subPackageA/report?type=2'
				})
			},
			//资产明细
			getWithdrawalRecord() {
				uni.navigateTo({
					url: '/pages/my/distribution/withdrawalRecord?index=2'
				})
			},

			//提现
			getWithdrawal() {

				if (Number(this.brokerageUser.brokerage) <= 0) {
					this.$sun.toast("暂无提现佣金", 'none');
					return;
				}

				uni.navigateTo({
					url: '/pages/my/distribution/withdrawal'
				})
			},

			//帮助教程
			getCourse() {
				uni.navigateTo({
					url: '/pages/my/course'
				})
			},

			//资产订单
			getAssets() {
				uni.navigateTo({
					url: '/pages/market/order'
				})
			},

			//克隆设置
			async getCloneSet() {
				const result = await this.$http.post({
					url: this.$api.cloneSet
				});
				if (result.errno == 0) {
					this.cloneSet = result.data;
					uni.setStorageSync('cloneSet', result.data);
				}
			},

			//转发设置
			async getRepostSet() {
				const result = await this.$http.post({
					url: this.$api.repostSet
				});
				if (result.errno == 0) {
					this.repostSetIsOpen = result.data.is_open;
				}
			},

			//会员设置
			async getMemberSet() {
				const result = await this.$http.post({
					url: this.$api.memberSet
				});
				if (result.errno == 0) {
					this.memberSet = result.data;
				}
			},

			//转发数据统计
			getStatistics() {
				uni.navigateTo({
					url: '/pages/works/statistics'
				})
			},

			//账户明细
			getConsumption() {
				uni.navigateTo({
					url: '/pages/my/consumption'
				})
			},

			//分销中心
			async getBrokerageIndex() {
				const result = await this.$http.post({
					url: this.$api.brokerageIndex,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					this.brokerageUser = result.data;
				}
			},

			//合伙人
			getPartner() {
				if (uni.getStorageSync('uid')) {

					//正常
					if (this.user.partner_status == 2 && this.user.partner_freeze == 1) {
						uni.setStorageSync('partnerId', this.user.partner_id);
						uni.navigateTo({
							url: '/pages/my/partner/index'
						})
					}
					//冻结
					if (this.user.partner_status == 2 && this.user.partner_freeze == 2) {
						uni.showModal({
							content: "您的合伙人已冻结,如有疑问请联系管理员!",
							confirmText: "确认",
							showCancel: false,
							success: (res) => {
								if (res.confirm) {

								} else if (res.cancel) {

								}
							}
						})
					}
					//未申请
					if (this.user.partner_status == 0) {
						uni.navigateTo({
							url: '/pages/my/partner/partner'
						})
					}
					//审核中
					if (this.user.partner_status == 1) {
						uni.showModal({
							content: "您的申请正在审核中,请耐心等待!",
							confirmText: "确认",
							showCancel: false,
							success: (res) => {
								if (res.confirm) {

								} else if (res.cancel) {

								}
							}
						})
					}
					//驳回
					if (this.user.partner_status == 3) {
						uni.showModal({
							title: '审核驳回',
							content: this.user.partner_refuse,
							cancelText: "确认",
							confirmText: "重新申请",
							success: (res) => {
								if (res.confirm) {
									this.updataStatus();
								} else if (res.cancel) {

								}
							}
						})
					}

				} else {
					uni.navigateTo({
						url: '/pages/auth/auth?type=1'
					})
				}
			},

			//合伙人状态变更
			async updataStatus() {
				const result = await this.$http.post({
					url: this.$api.partnerUpdateStatus,
					data: {
						partner_id: this.user.partner_id,
						name: this.user.partner_name,
						telphone: this.user.partner_telphone
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message, 'none');
				} else {
					this.$sun.toast("认证状态变更失败", 'none');
				}
			},

			//合伙人设置
			async getPartnerSet() {
				const result = await this.$http.post({
					url: this.$api.partnerSet
				});
				if (result.errno == 0) {
					this.partner_is_open = result.data.is_open;
				}
			},

			//会员
			getMember() {
				uni.navigateTo({
					url: '/pages/my/member'
				})
			},

			//分销
			getDistribution() {
				uni.navigateTo({
					url: '/pages/my/distribution/distribution'
				})
			},

			//分销设置
			async getBrokerageSet() {
				const result = await this.$http.post({
					url: this.$api.brokerageSet,
				});
				if (result.errno == 0) {
					this.is_open = result.data.is_open;
					this.cash_open = result.data.cash_open;
					this.is_shareholder_swich = result.data.is_shareholder_swich;
				}
			},

			//联系客服
			getContactUs() {
				uni.navigateTo({
					url: '/pages/my/contactUs'
				})
			},

			//用户信息
			async userInfo() {
				const result = await this.$http.post({
					url: this.$api.userInfo,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					this.user = result.data;
					uni.setStorageSync('isMember', result.data.is_member);
				}
			},

			//系统设置
			async getSystem() {
				const result = await this.$http.post({
					url: this.$api.system
				});
				if (result.errno == 0) {
					this.systems = result.data;
					uni.setStorageSync('system', result.data);
				}
			},

			getAuth(type) {
				if (type === 2) {
					return
				}
				uni.navigateTo({
					url: '/pages/auth/auth?type=' + type
				})
			},

			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
						// this.windowHeight = res.windowHeight * 2 - 800;
					},
					fail(err) {
						// console.log(err);
					}
				})
			},

		}
	}
</script>

<style lang="scss">
	.button-phone {
		width: 710rpx;
		margin: 0 auto !important;
	}

	.img-329 {
		width: 36rpx;
		height: 36rpx;
		margin-right: 8rpx;
	}

	.color_969696 {
		color: #969696;
		font-size: 26rpx;
		margin-bottom: 8rpx;
	}

	.width_236rpx-center {
		width: 223rpx;
	}

	.withdrawal {
		border-radius: 100rpx;
		background: rgb(255, 44, 37);
		width: 166rpx;
		padding: 18rpx 0;
		text-align: center;
		font-size: 30rpx;
		margin-left: auto;
	}

	.img-242 {
		width: 20rpx;
		height: 20rpx;
		margin-left: 10rpx;
	}

	.img-257 {
		width: 710rpx;
		height: 310rpx;
		background-repeat: no-repeat;
		background-size: contain;
		margin: 30rpx 20rpx 20rpx;
		padding: 18rpx 20rpx;
	}

	button {
		background: #24232A;
		border: none;
		margin: 0 !important;
		padding: 0 !important;
		// padding: 0px 20rpx;
		// font-size: 30rpx;
		// margin-top: 10rpx;
		width: 100%;
		// display: block;
		// color: #000 !important;
	}

	button::after {
		border: none;
	}

	.width_166rpx-center {
		width: 176rpx;
		margin-bottom: 40rpx;
		text-align: center;
		height: 126rpx;
	}

	.m-line {
		width: 8rpx;
		height: 22rpx;
		background-color: #FFF;
		border-radius: 4rpx;
		margin-right: 10rpx;
	}

	.img-209 {
		width: 710rpx;
		height: 56rpx;
	}

	.img-176 {
		width: 90rpx;
		height: 90rpx;
		border-radius: 50%;
		margin-bottom: 6rpx;
	}

	.my-line {
		background-color: #FFF;
		width: 8rpx;
		height: 22rpx;
		margin-right: 12rpx;
		border-radius: 10rpx;
	}

	.list-public {
		background-color: #24232A;
		padding: 20rpx 0;
	}

	.img-31 {
		width: 108rpx;
		height: 28rpx;
		margin-left: 20rpx;
	}

	.img-32 {
		width: 28rpx;
		height: 24rpx;
		margin-right: 6rpx;
	}

	.img-30 {
		width: 100rpx;
		height: 100rpx;
		border-radius: 100rpx;
		margin-right: 20rpx;
	}

	.img-21 {
		width: 28rpx;
		height: 28rpx;
		margin-left: auto;
	}

	.img-14 {
		width: 80rpx;
		height: 80rpx;

	}

	.frame-top {
		border-bottom: 1px solid #2C2C2C;
		padding: 30rpx 0;
	}

	.frame {
		width: 250rpx;
		text-align: center;
		color: #FFF;
	}

	.m-tips {
		background: linear-gradient(69.64deg, rgb(0, 53, 255), rgb(245, 75, 113));
		-webkit-background-clip:
			text;
		-webkit-text-fill-color:
			transparent;
		background-clip:
			text;
		text-fill-color:
			transparent;
		font-size: 28rpx;
		font-weight: 400;
		letter-spacing: 0%;
		text-align: left;
	}

	.color_874F03 {
		font-size: 36rpx;
		font-weight: 700;
		color: #874F03;
	}

	.renew {
		width: 164rpx;
		text-align: center;
		background-color: #000000;
		border-radius: 100rpx;
		padding: 22rpx 0;
		color: #FDD6A8;
		margin-left: auto;
		font-size: 28rpx;
	}

	.img-13 {
		width: 50rpx;
		height: 50rpx;
		margin-right: 12rpx;
	}

	.member-bg {
		width: 710rpx;
		height: 250rpx;
		margin: 60rpx 20rpx 0;
		padding: 34rpx 20rpx 0;
		background-repeat: no-repeat;
		background-size: contain;
	}

	.bg {
		width: 750rpx;
		height: 464rpx;
		background-repeat: no-repeat;
		background-size: cover;
	}

	.iconDizhssi {
		position: absolute;
		z-index: 999;
		font-size: 32rpx;
		// font-weight: bold;
		color: #FFFFFF;
	}

	page {
		background-color: #111317;
		border: none;
	}
</style>