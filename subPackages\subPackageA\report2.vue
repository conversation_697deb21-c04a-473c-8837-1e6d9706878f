<template>
	<view class="r-top">
		<view class="step-indicator" v-if="[2].includes(type)">
			<view v-for="(step, index) in steps" :key="index" v-if="index !== 0" class="step-item"
				@click="goToStep(index)" :class="{ 'active': currentStep >= index, 'completed': currentStep > index }">
				<view class="step-circle">{{ index + 1 }}</view>
				<view class="step-title">{{ step.title }}</view>
				<view class="step-status">{{ getStatus(index) }}</view>
				<view class="step-line" v-if="index < steps.length - 1"></view>
			</view>
		</view>
		<view class="report-content" id="report-content">
			<!-- Page content here -->
			<towxml :nodes="formattedContent" />
		</view>
		<!-- 按钮组 -->
		<view class="button-group" v-if="[1,2,3].includes(type) && isEnd">
			<view class="m-but" @click="reset">重新生成</view>
			<view class="m-but download-but" @click="downloadReport">下载报告</view>
		</view>

		<!-- wxml-to-canvas组件 -->
		<wxml-to-canvas class="canvas-widget" style="position: fixed; top: -9999px; left: -9999px;"></wxml-to-canvas>

		<!-- 添加全屏加载组件 -->
		<full-screen-loading :show="isLoading" text="正在加载..."></full-screen-loading>
	</view>
</template>

<script>
	import {
		decodedString
	} from './utils/decodedString.js'
	// 导入全屏加载组件
	import FullScreenLoading from './components/full-screen-loading/full-screen-loading.vue';
	const towxml = require('./wxcomponents/towxml/index');
	export default {
		components: {
			FullScreenLoading
		},
		data() {
			return {
				currentStep: 1, // 0 = first step, 1 = second step, etc.
				steps: [{
						title: '行业诊断报告',
						completed: false,
						content: ''
					},
					{
						title: '详细运营方案',
						completed: false,
						content: ''
					},
					{
						title: 'AI企业运营计划督导',
						completed: false,
						content: ''
					}
				],
				requestTask: null,
				userData: '',
				streamData: '',
				isLoading: false, // 初始状态为加载中
				type: null,
				isEnd: false,
				meeting_id: null,
			}
		},
		computed: {
			formattedContent() {
				// 处理换行符，确保在小程序中正确显示
				if (!this.streamData) return '';


				// 将普通换行符转换为HTML换行
				let formatted = this.streamData


				// 确保小程序富文本能够正确解析
				return towxml(formatted, 'markdown', {
					theme: 'dark'
				});
			}
		},
		methods: {
			async reset() {
				if (this.type === 3) {
					// 重新生成目标人群
					this.startStream()
					return
				}

				const result = await this.$http.post({
					url: this.$api.getProject,
					data: {
						uid: uni.getStorageSync('uid'),

					}
				});
				if (result.errno == 0) {
					if (!result.data) {
						this.$sun.toast('请先前往AI立项，生成立项数据', 'none');
						return
					}
					this.userData = `行业:${result.data.industry}
客户群体:${result.data.target_customers}
客户痛点:${result.data.pain_points}
行业优势:${result.data.industry_advantages}
解决方案:${result.data.solution_requirements}
客户自发的在帮您做介绍:${result.data.custom_promotion}
每天发布多少条企业内容到公域平台:${result.data.launch_recommendations}
私域池塘里沉淀了多少客户量:${result.data.new_users_last_year}
去年新增客户量是多少人:${result.data.new_users_last_year}
去年营业额是多少:${result.data.annual_revenue}`
					if (this.type === 1) {
						this.startStream(0)
					} else if (this.type === 2) {
						this.steps = [{
								title: '行业诊断报告',
								completed: false,
								content: ''
							},
							{
								title: '详细运营方案',
								completed: false,
								content: ''
							},
							{
								title: 'AI企业运营计划督导',
								completed: false,
								content: ''
							}
						]
						this.startStream(1)
					}
				} else {
					this.$sun.toast(result.message, 'none');
				}
			},

			async getMeetingReport() {
				let res = await this.$http.post({
					url: this.$api.getMeetingInfo,
					data: {
						uid: uni.getStorageSync('uid'),
						meeting_id: this.meeting_id
					}
				});
				if (res.errno === 0) {
					this.streamData = res.data.abstract;
					this.isEnd = true;
				} else {
					this.$sun.toast(res.message, 'none');
				}
			},

			async getReport() {
				let res = await this.$http.post({
					url: this.$api.getAIProjectResult,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (res.errno === 0) {
					if (res.data) {
						let data = res.data;
						if (this.type === 3) {
							if (data.course_analysis) {
								this.streamData = data.course_analysis
								this.isEnd = true;
							} else {
								this.startStream()
							}
							this.isLoading = false;
							return
						}
						this.steps[0].content = data.industry_diagnosis;
						this.steps[1].content = data.operation_plan;
						this.steps[2].content = data.plan_supervision;

						this.steps[0].completed = true;
						this.steps[1].completed = true;
						this.steps[2].completed = true;
						this.isEnd = true;
						if (this.type === 2) {
							this.goToStep(1);
						} else {
							this.goToStep(0);
						}

					} else {
						this.reset()
						// uni.redirectTo({
						// 	url: '/pages/my/userInfo?zIndex=2'
						// })
					}
				} else {
					this.$sun.toast(res.message, 'none');
					uni.navigateBack();
				}
				this.isLoading = false;
			},
			goToStep(index) {
				// 当前节点正在生成，或者当前节点未完成，则不进行跳转
				if (!this.steps[index].completed || !this.steps[this.currentStep].completed) {
					this.$sun.toast('请等待当前节点生成完成', 'none');
					return;
				}
				this.currentStep = index;
				this.streamData = this.steps[index].content;
			},
			startStream(index) {
				this.streamData = '';
				let url = '';
				this.isEnd = false;

				if (index == 0) {
					url = this.$api.getAIProjectIndustryReport
				} else if (index == 1) {
					url = this.$api.getAIProjectOperationPlan
				} else if (index == 2) {
					url = this.$api.getAIProjectOperationSupervision
				}

				let data = {
					uid: uni.getStorageSync('uid'), // 请求参数
					content: this.userData
				}

				if (this.type === 3) {
					url = this.$api.getAIProjectCustomer
					data = {
						uid: uni.getStorageSync('uid')
					}
				} else if (this.type === 4) {
					url = this.$api.getAIProjectCustomer
					data = {
						uid: uni.getStorageSync('uid')
					}
				}

				// 使用流式请求
				this.requestTask = this.$http.requestStream(url, "POST", data, {
					onChunk: (data) => {
						// 处理接收到的数据块
						try {
							// 检查数据是否为ArrayBuffer类型
							if (data instanceof ArrayBuffer) {
								// 将ArrayBuffer转换为字符串
								let text = '';
								try {
									// 使用解码函数处理ArrayBuffer
									text = decodedString(data);
									if (!text) {
										throw new Error("解码结果为空");
									}
								} catch (decodeError) {
									console.error("解码ArrayBuffer失败:", decodeError);
									// 尝试使用备用方法
									const buffer = new Uint8Array(data);
									text = String.fromCharCode.apply(null, buffer);
								}

								// 追加到正确的目标属性
								this.streamData += text;
							} else if (typeof data === 'string') {
								// 如果是字符串，直接追加到目标属性
								this.streamData += data;
							} else {
								console.warn("收到未知类型的数据块:", data);
							}

							// 滚动到最新内容
							this.scrollToBottom();
						} catch (error) {
							console.error("处理数据块出错:", error);
						}
					},
					onComplete: () => {
						console.log(`类型${index}请求完成`);
						if (this.type === 3) {
							this.isEnd = true;
							return
						}
						// 标记请求完成，使用数字类型的键
						this.steps[index].completed = true;
						this.steps[index].content = this.streamData;
						if (index === 2) {
							this.isEnd = true;
						} else {
							this.goToNextStep();
						}
					},
					onError: (err) => {
						uni.hideLoading();
						console.error(`类型${index}流式请求错误:`, err);
					}
				});
			},
			getStatus(index) {
				if (this.currentStep > index || this.steps[index].completed) {
					return '已完成';
				} else if (this.currentStep === index) {
					return '生成中...';
				} else {
					return '等待生成';
				}
			},
			goToNextStep() {
				if (this.type === 1) {
					return
				}
				if (this.currentStep < this.steps.length - 1) {
					this.steps[this.currentStep].completed = true;
					this.currentStep++;
					this.startStream(this.currentStep);
				}
			},
			// 中止请求
			stopStream() {
				if (this.requestTask) {
					this.requestTask.cancel();
					this.requestTask = null;
					console.log('已中止请求');
				}
			},
			// 滚动到内容底部
			scrollToBottom() {
				// 使用nextTick确保DOM已更新
				this.$nextTick(() => {
					const reportContent = uni.createSelectorQuery().in(this).select('#report-content');
					reportContent.boundingClientRect(data => {
						if (data) {
							uni.pageScrollTo({
								scrollTop: data.height,
								duration: 300
							});
						}
					}).exec();
				});
			},

			// 下载报告
			async downloadReport() {
				try {
					uni.showLoading({
						title: '正在生成图片...',
						mask: true
					});

					// 获取wxml-to-canvas组件实例
					this.canvasWidget = this.selectComponent('.canvas-widget');
					if (!this.canvasWidget) {
						throw new Error('canvas组件未找到');
					}

					// 生成wxml模板和样式
					const {wxml, style} = this.generateCanvasContent();

					// 渲染到canvas
					await this.canvasWidget.renderToCanvas({wxml, style});

					// 转换为临时文件
					const result = await this.canvasWidget.canvasToTempFilePath({
						fileType: 'png',
						quality: 1
					});

					uni.hideLoading();

					// 保存到相册
					await this.saveImageToAlbum(result.tempFilePath);

				} catch (error) {
					uni.hideLoading();
					console.error('下载报告失败:', error);
					uni.showToast({
						title: '生成图片失败',
						icon: 'none'
					});
				}
			},

			// 生成canvas内容
			generateCanvasContent() {
				// 获取当前显示的内容
				let content = '';
				if (this.type === 3) {
					content = this.streamData;
				} else if (this.type === 1) {
					content = this.steps[0].content;
				} else if (this.type === 2) {
					content = this.steps[this.currentStep].content;
				} else {
					content = this.streamData;
				}

				// 解析markdown内容为结构化数据
				const parsedContent = this.parseMarkdownContent(content);

				// 生成wxml模板
				const wxml = `
					<view class="report-container">
						<view class="report-header">
							<text class="report-title">${this.getReportTitle()}</text>
						</view>
						<view class="report-content">
							${this.generateContentWxml(parsedContent)}
						</view>
						<view class="report-footer">
							<text class="footer-text">由AI智能生成</text>
						</view>
					</view>
				`;
console.log();

				// 样式配置
				const style = {
					reportContainer: {
						width: 750,
						height: this.contentHeight || 1000,
						backgroundColor: '#111D37',
						padding: 30,
						flexDirection: 'column'
					},
					reportHeader: {
						width: 690,
						height: 80,
						marginBottom: 30,
						justifyContent: 'center',
						alignItems: 'center'
					},
					reportTitle: {
						width: 690,
						height: 80,
						fontSize: 32,
						color: '#FFFFFF',
						textAlign: 'center',
						verticalAlign: 'middle',
						fontWeight: 'bold'
					},
					reportContent: {
						width: 690,
						flexDirection: 'column',
						marginBottom: 30
					},
					// 空行
					spaceLine: {
						width: 690,
						height: 20
					},
					// 标题样式
					header1: {
						width: 690,
						height: 60,
						fontSize: 36,
						color: '#FFFFFF',
						fontWeight: 'bold',
						marginBottom: 15,
						lineHeight: '60px'
					},
					header2: {
						width: 690,
						height: 50,
						fontSize: 32,
						color: '#E5E5E5',
						fontWeight: 'bold',
						marginBottom: 12,
						lineHeight: '50px'
					},
					header3: {
						width: 690,
						height: 40,
						fontSize: 28,
						color: '#D0D0D0',
						fontWeight: 'bold',
						marginBottom: 10,
						lineHeight: '40px'
					},
					header4: {
						width: 690,
						height: 40,
						fontSize: 26,
						color: '#C0C0C0',
						fontWeight: 'bold',
						marginBottom: 8,
						lineHeight: '40px'
					},
					header5: {
						width: 690,
						height: 40,
						fontSize: 24,
						color: '#B0B0B0',
						fontWeight: 'bold',
						marginBottom: 6,
						lineHeight: '40px'
					},
					header6: {
						width: 690,
						height: 40,
						fontSize: 22,
						color: '#A0A0A0',
						fontWeight: 'bold',
						marginBottom: 4,
						lineHeight: '40px'
					},
					// 列表样式
					listItem: {
						width: 690,
						height: 35,
						flexDirection: 'row',
						alignItems: 'center',
						marginBottom: 5
					},
					listBullet: {
						width: 20,
						height: 35,
						fontSize: 20,
						color: '#9D5DFF',
						lineHeight: '35px'
					},
					listText: {
						width: 670,
						height: 35,
						fontSize: 22,
						color: '#E5E5E5',
						lineHeight: '35px'
					},
					// 引用样式
					quoteContainer: {
						width: 690,
						height: 40,
						backgroundColor: '#1A2332',
						borderRadius: 8,
						padding: 10,
						marginBottom: 10
					},
					quoteText: {
						width: 670,
						height: 40,
						fontSize: 22,
						color: '#B8B8B8',
						fontStyle: 'italic',
						lineHeight: '40px'
					},
					// 段落样式
					paragraphText: {
						width: 690,
						height: 35,
						fontSize: 24,
						color: '#E5E5E5',
						lineHeight: '35px',
						marginBottom: 8
					},
					// 行内格式
					boldText: {
						fontWeight: 'bold',
						color: '#FFFFFF'
					},
					italicText: {
						fontStyle: 'italic',
						color: '#D0D0D0'
					},
					codeText: {
						backgroundColor: '#2A324A',
						color: '#9D5DFF',
						borderRadius: 4,
						padding: 2
					},
					reportFooter: {
						width: 690,
						height: 60,
						justifyContent: 'center',
						alignItems: 'center'
					},
					footerText: {
						width: 690,
						height: 60,
						fontSize: 20,
						color: '#999999',
						textAlign: 'center',
						verticalAlign: 'middle'
					}
				};

				return {wxml, style};
			},

			// 获取报告标题
			getReportTitle() {
				if (this.type === 3) {
					return '目标人群分析报告';
				} else if (this.type === 1) {
					return 'AI行业诊断报告';
				} else if (this.type === 2) {
					const titles = ['行业诊断报告', '详细运营方案', 'AI企业运营计划督导'];
					return titles[this.currentStep] || 'AI商业定位报告';
				} else if (this.type === 4) {
					return '热点话题分析';
				} else if (this.type === 5) {
					return '会议报告';
				}
				return 'AI分析报告';
			},

			// 解析markdown内容
			parseMarkdownContent(markdown) {
				if (!markdown) return [];

				const elements = [];
				const lines = markdown.split('\n');

				for (let i = 0; i < lines.length; i++) {
					const line = lines[i];

					// 跳过空行
					if (line.trim() === '') {
						elements.push({ type: 'space', content: '' });
						continue;
					}

					// 解析标题 (# ## ### 等)
					const headerMatch = line.match(/^(#{1,6})\s+(.+)$/);
					if (headerMatch) {
						const level = headerMatch[1].length;
						const text = headerMatch[2].trim();
						elements.push({
							type: 'header',
							level: level,
							content: text
						});
						continue;
					}

					// 解析列表项 (- * + 或 1. 2. 等)
					const listMatch = line.match(/^\s*[-*+]\s+(.+)$/) || line.match(/^\s*\d+\.\s+(.+)$/);
					if (listMatch) {
						const text = listMatch[1].trim();
						elements.push({
							type: 'list',
							content: this.parseInlineMarkdown(text)
						});
						continue;
					}

					// 解析引用 (> 开头)
					const quoteMatch = line.match(/^>\s+(.+)$/);
					if (quoteMatch) {
						const text = quoteMatch[1].trim();
						elements.push({
							type: 'quote',
							content: this.parseInlineMarkdown(text)
						});
						continue;
					}

					// 普通段落
					if (line.trim()) {
						elements.push({
							type: 'paragraph',
							content: this.parseInlineMarkdown(line.trim())
						});
					}
				}

				return elements;
			},

			// 解析行内markdown格式（粗体、斜体等）
			parseInlineMarkdown(text) {
				const parts = [];
				let currentText = text;
				let index = 0;

				while (index < currentText.length) {
					// 查找粗体 **text**
					const boldMatch = currentText.substring(index).match(/^\*\*(.*?)\*\*/);
					if (boldMatch) {
						if (boldMatch[1]) {
							parts.push({ type: 'bold', text: boldMatch[1] });
						}
						index += boldMatch[0].length;
						continue;
					}

					// 查找斜体 *text*
					const italicMatch = currentText.substring(index).match(/^\*(.*?)\*/);
					if (italicMatch) {
						if (italicMatch[1]) {
							parts.push({ type: 'italic', text: italicMatch[1] });
						}
						index += italicMatch[0].length;
						continue;
					}

					// 查找代码 `text`
					const codeMatch = currentText.substring(index).match(/^`(.*?)`/);
					if (codeMatch) {
						if (codeMatch[1]) {
							parts.push({ type: 'code', text: codeMatch[1] });
						}
						index += codeMatch[0].length;
						continue;
					}

					// 普通文本
					let normalText = '';
					while (index < currentText.length &&
						   !currentText.substring(index).match(/^(\*\*|\*|`)/) ) {
						normalText += currentText[index];
						index++;
					}

					if (normalText) {
						parts.push({ type: 'normal', text: normalText });
					}
				}

				return parts.length > 0 ? parts : [{ type: 'normal', text: text }];
			},

			// 生成内容的wxml
			generateContentWxml(elements) {
				let wxml = '';
				let currentHeight = 0;

				elements.forEach((element) => {
					switch (element.type) {
						case 'space':
							wxml += `<view class="space-line"></view>`;
							currentHeight += 20;
							break;

						case 'header':
							const headerClass = `header-${element.level}`;
							wxml += `<text class="${headerClass}">${element.content}</text>`;
							currentHeight += element.level === 1 ? 60 : (element.level === 2 ? 50 : 40);
							break;

						case 'list':
							wxml += `<view class="list-item">`;
							wxml += `<text class="list-bullet">• </text>`;
							wxml += this.generateInlineWxml(element.content, 'list-text');
							wxml += `</view>`;
							currentHeight += 35;
							break;

						case 'quote':
							wxml += `<view class="quote-container">`;
							wxml += this.generateInlineWxml(element.content, 'quote-text');
							wxml += `</view>`;
							currentHeight += 40;
							break;

						case 'paragraph':
							wxml += this.generateInlineWxml(element.content, 'paragraph-text');
							currentHeight += 35;
							break;
					}
				});

				// 更新容器高度
				this.contentHeight = Math.max(800, currentHeight + 200);

				return wxml;
			},

			// 生成行内元素的wxml
			generateInlineWxml(parts, baseClass) {
				return parts.map(part => {
					switch (part.type) {
						case 'bold':
							return `<text class="${baseClass} bold-text">${part.text}</text>`;
						case 'italic':
							return `<text class="${baseClass} italic-text">${part.text}</text>`;
						case 'code':
							return `<text class="${baseClass} code-text">${part.text}</text>`;
						case 'normal':
						default:
							return `<text class="${baseClass}">${part.text}</text>`;
					}
				}).join('');
			},

			// 保存图片到相册
			async saveImageToAlbum(filePath) {
				return new Promise((resolve, reject) => {
					uni.saveImageToPhotosAlbum({
						filePath: filePath,
						success: (res) => {
							uni.showToast({
								title: '报告已保存到相册',
								icon: 'success'
							});
							resolve(res);
						},
						fail: (err) => {
							console.error('保存图片失败:', err);
							// 处理权限问题
							if (err.errMsg && err.errMsg.indexOf('auth deny') >= 0) {
								uni.showModal({
									title: '需要相册权限',
									content: '保存图片需要访问您的相册，请在设置中开启权限',
									confirmText: '去设置',
									success: (modalRes) => {
										if (modalRes.confirm) {
											uni.openSetting({
												success: (settingRes) => {
													console.log('设置页面返回:', settingRes);
												},
												fail: (settingErr) => {
													console.error('打开设置失败:', settingErr);
												}
											});
										}
									}
								});
							} else {
								uni.showToast({
									title: '保存失败',
									icon: 'none'
								});
							}
							reject(err);
						}
					});
				});
			},
		},
		onLoad(options) {
			if (options.type) {
				this.type = Number(options.type);
			}
			if (this.type === 3) {
				uni.setNavigationBarTitle({
					title: '目标人群'
				})
			} else if (this.type === 4) {
				uni.setNavigationBarTitle({
					title: '热点话题'
				})
			} else if (this.type === 5) {
				uni.setNavigationBarTitle({
					title: '会议报告'
				})
				this.meeting_id = options.meeting_id;
				this.getMeetingReport();
			} else {
				uni.setNavigationBarTitle({
					title: 'AI诊断'
				})
			}
			if ([1, 2, 3].includes(this.type)) {
				this.isLoading = true;
				this.getReport();
			}
			if (this.type === 2) {
				uni.setNavigationBarTitle({
					title: 'AI商业定位'
				})
				// this.userData = uni.getStorageSync('userData');
				// this.startStream(0);
			} else if (this.type === 4) {
				// 热点话题
				this.startStream();
			}
		},
		onUnload() {
			this.stopStream();
		}
	}
</script>

<style lang="scss">
	page {
		border-top: none;
		background-color: #080E1E;
		overflow-x: hidden;
	}

	.button-group {
		margin-top: 80rpx;
		display: flex;
		justify-content: space-between;
		gap: 20rpx;
	}

	.m-but {
		font-size: 32rpx;
		color: #FFF;
		padding: 30rpx 0;
		flex: 1;
		text-align: center;
		border-radius: 10rpx;
		box-shadow: 0px -1px 11px 4px rgba(127, 27, 255, 0.2);
		background: linear-gradient(96.34deg, rgb(210, 174, 255), rgb(154, 93, 228));
	}

	.download-but {
		background: linear-gradient(96.34deg, rgb(174, 255, 210), rgb(93, 228, 154));
		box-shadow: 0px -1px 11px 4px rgba(27, 255, 127, 0.2);
	}

	.r-top {
		padding: 30rpx 20rpx;
	}

	.step-indicator {
		display: flex;
		justify-content: space-between;
		margin-bottom: 40rpx;
		padding: 30rpx;
		background-color: #111D37;
		border-radius: 12rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
		position: relative;
	}

	.step-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		flex: 1;
		z-index: 1;
	}

	.step-circle {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		background: #2A324A;
		display: flex;
		align-items: center;
		justify-content: center;
		color: rgba(255, 255, 255, 0.7);
		font-weight: bold;
		margin-bottom: 10rpx;
		transition: all 0.3s ease;
	}

	.step-item.active .step-circle,
	.step-item.completed .step-circle {
		background: linear-gradient(96.34deg, rgb(210, 174, 255), rgb(154, 93, 228));
		box-shadow: 0 0 12rpx rgba(154, 93, 228, 0.6);
	}

	.step-title {
		font-size: 28rpx;
		font-weight: bold;
		margin-bottom: 6rpx;
		text-align: center;
		color: rgba(255, 255, 255, 0.85);
	}

	.step-status {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.5);
	}

	.step-item.active .step-status {
		color: rgb(210, 174, 255);
		font-weight: 500;
	}

	.step-item.completed .step-status {
		color: #5fe6ae;
	}

	.step-line {
		position: absolute;
		top: 30rpx;
		right: -50%;
		width: 100%;
		height: 4rpx;
		background-color: #2A324A;
		z-index: -1;
	}

	.step-item.completed .step-line {
		background: linear-gradient(96.34deg, rgb(210, 174, 255), rgb(154, 93, 228));
	}

	.report-content {
		background-color: #111D37;
		border-radius: 12rpx;
		padding-bottom: 20rpx;
	}
</style>