<template>
	<view class="r-top">
		<view class="step-indicator" v-if="[2].includes(type)">
			<view v-for="(step, index) in steps" :key="index" v-if="index !== 0" class="step-item"
				@click="goToStep(index)" :class="{ 'active': currentStep >= index, 'completed': currentStep > index }">
				<view class="step-circle">{{ index + 1 }}</view>
				<view class="step-title">{{ step.title }}</view>
				<view class="step-status">{{ getStatus(index) }}</view>
				<view class="step-line" v-if="index < steps.length - 1"></view>
			</view>
		</view>
		<view class="report-content" id="report-content">
			<!-- Page content here -->
			<towxml :nodes="formattedContent" />
		</view>
		<!-- 按钮组 -->
		<view class="button-group" v-if="[1,2,3].includes(type) && isEnd">
			<view class="m-but" @click="reset">重新生成</view>
			<view class="m-but download-but" @click="downloadReport">下载报告</view>
		</view>

		<!-- wxml-to-canvas组件 -->
		<wxml-to-canvas class="canvas-widget" style="position: fixed; top: -9999px; left: -9999px;"></wxml-to-canvas>

		<!-- 添加全屏加载组件 -->
		<full-screen-loading :show="isLoading" text="正在加载..."></full-screen-loading>
	</view>
</template>

<script>
	import {
		decodedString
	} from './utils/decodedString.js'
	// 导入全屏加载组件
	import FullScreenLoading from './components/full-screen-loading/full-screen-loading.vue';
	const towxml = require('./wxcomponents/towxml/index');
	export default {
		components: {
			FullScreenLoading
		},
		data() {
			return {
				currentStep: 1, // 0 = first step, 1 = second step, etc.
				steps: [{
						title: '行业诊断报告',
						completed: false,
						content: ''
					},
					{
						title: '详细运营方案',
						completed: false,
						content: ''
					},
					{
						title: 'AI企业运营计划督导',
						completed: false,
						content: ''
					}
				],
				requestTask: null,
				userData: '',
				streamData: '',
				isLoading: false, // 初始状态为加载中
				type: null,
				isEnd: false,
				meeting_id: null,
			}
		},
		computed: {
			formattedContent() {
				// 处理换行符，确保在小程序中正确显示
				if (!this.streamData) return '';


				// 将普通换行符转换为HTML换行
				let formatted = this.streamData


				// 确保小程序富文本能够正确解析
				return towxml(formatted, 'markdown', {
					theme: 'dark'
				});
			}
		},
		methods: {
			async reset() {
				if (this.type === 3) {
					// 重新生成目标人群
					this.startStream()
					return
				}

				const result = await this.$http.post({
					url: this.$api.getProject,
					data: {
						uid: uni.getStorageSync('uid'),

					}
				});
				if (result.errno == 0) {
					if (!result.data) {
						this.$sun.toast('请先前往AI立项，生成立项数据', 'none');
						return
					}
					this.userData = `行业:${result.data.industry}
客户群体:${result.data.target_customers}
客户痛点:${result.data.pain_points}
行业优势:${result.data.industry_advantages}
解决方案:${result.data.solution_requirements}
客户自发的在帮您做介绍:${result.data.custom_promotion}
每天发布多少条企业内容到公域平台:${result.data.launch_recommendations}
私域池塘里沉淀了多少客户量:${result.data.new_users_last_year}
去年新增客户量是多少人:${result.data.new_users_last_year}
去年营业额是多少:${result.data.annual_revenue}`
					if (this.type === 1) {
						this.startStream(0)
					} else if (this.type === 2) {
						this.steps = [{
								title: '行业诊断报告',
								completed: false,
								content: ''
							},
							{
								title: '详细运营方案',
								completed: false,
								content: ''
							},
							{
								title: 'AI企业运营计划督导',
								completed: false,
								content: ''
							}
						]
						this.startStream(1)
					}
				} else {
					this.$sun.toast(result.message, 'none');
				}
			},

			async getMeetingReport() {
				let res = await this.$http.post({
					url: this.$api.getMeetingInfo,
					data: {
						uid: uni.getStorageSync('uid'),
						meeting_id: this.meeting_id
					}
				});
				if (res.errno === 0) {
					this.streamData = res.data.abstract;
					this.isEnd = true;
				} else {
					this.$sun.toast(res.message, 'none');
				}
			},

			async getReport() {
				let res = await this.$http.post({
					url: this.$api.getAIProjectResult,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (res.errno === 0) {
					if (res.data) {
						let data = res.data;
						if (this.type === 3) {
							if (data.course_analysis) {
								this.streamData = data.course_analysis
								this.isEnd = true;
							} else {
								this.startStream()
							}
							this.isLoading = false;
							return
						}
						this.steps[0].content = data.industry_diagnosis;
						this.steps[1].content = data.operation_plan;
						this.steps[2].content = data.plan_supervision;

						this.steps[0].completed = true;
						this.steps[1].completed = true;
						this.steps[2].completed = true;
						this.isEnd = true;
						if (this.type === 2) {
							this.goToStep(1);
						} else {
							this.goToStep(0);
						}

					} else {
						this.reset()
						// uni.redirectTo({
						// 	url: '/pages/my/userInfo?zIndex=2'
						// })
					}
				} else {
					this.$sun.toast(res.message, 'none');
					uni.navigateBack();
				}
				this.isLoading = false;
			},
			goToStep(index) {
				// 当前节点正在生成，或者当前节点未完成，则不进行跳转
				if (!this.steps[index].completed || !this.steps[this.currentStep].completed) {
					this.$sun.toast('请等待当前节点生成完成', 'none');
					return;
				}
				this.currentStep = index;
				this.streamData = this.steps[index].content;
			},
			startStream(index) {
				this.streamData = '';
				let url = '';
				this.isEnd = false;

				if (index == 0) {
					url = this.$api.getAIProjectIndustryReport
				} else if (index == 1) {
					url = this.$api.getAIProjectOperationPlan
				} else if (index == 2) {
					url = this.$api.getAIProjectOperationSupervision
				}

				let data = {
					uid: uni.getStorageSync('uid'), // 请求参数
					content: this.userData
				}

				if (this.type === 3) {
					url = this.$api.getAIProjectCustomer
					data = {
						uid: uni.getStorageSync('uid')
					}
				} else if (this.type === 4) {
					url = this.$api.getAIProjectCustomer
					data = {
						uid: uni.getStorageSync('uid')
					}
				}

				// 使用流式请求
				this.requestTask = this.$http.requestStream(url, "POST", data, {
					onChunk: (data) => {
						// 处理接收到的数据块
						try {
							// 检查数据是否为ArrayBuffer类型
							if (data instanceof ArrayBuffer) {
								// 将ArrayBuffer转换为字符串
								let text = '';
								try {
									// 使用解码函数处理ArrayBuffer
									text = decodedString(data);
									if (!text) {
										throw new Error("解码结果为空");
									}
								} catch (decodeError) {
									console.error("解码ArrayBuffer失败:", decodeError);
									// 尝试使用备用方法
									const buffer = new Uint8Array(data);
									text = String.fromCharCode.apply(null, buffer);
								}

								// 追加到正确的目标属性
								this.streamData += text;
							} else if (typeof data === 'string') {
								// 如果是字符串，直接追加到目标属性
								this.streamData += data;
							} else {
								console.warn("收到未知类型的数据块:", data);
							}

							// 滚动到最新内容
							this.scrollToBottom();
						} catch (error) {
							console.error("处理数据块出错:", error);
						}
					},
					onComplete: () => {
						console.log(`类型${index}请求完成`);
						if (this.type === 3) {
							this.isEnd = true;
							return
						}
						// 标记请求完成，使用数字类型的键
						this.steps[index].completed = true;
						this.steps[index].content = this.streamData;
						if (index === 2) {
							this.isEnd = true;
						} else {
							this.goToNextStep();
						}
					},
					onError: (err) => {
						uni.hideLoading();
						console.error(`类型${index}流式请求错误:`, err);
					}
				});
			},
			getStatus(index) {
				if (this.currentStep > index || this.steps[index].completed) {
					return '已完成';
				} else if (this.currentStep === index) {
					return '生成中...';
				} else {
					return '等待生成';
				}
			},
			goToNextStep() {
				if (this.type === 1) {
					return
				}
				if (this.currentStep < this.steps.length - 1) {
					this.steps[this.currentStep].completed = true;
					this.currentStep++;
					this.startStream(this.currentStep);
				}
			},
			// 中止请求
			stopStream() {
				if (this.requestTask) {
					this.requestTask.cancel();
					this.requestTask = null;
					console.log('已中止请求');
				}
			},
			// 滚动到内容底部
			scrollToBottom() {
				// 使用nextTick确保DOM已更新
				this.$nextTick(() => {
					const reportContent = uni.createSelectorQuery().in(this).select('#report-content');
					reportContent.boundingClientRect(data => {
						if (data) {
							uni.pageScrollTo({
								scrollTop: data.height,
								duration: 300
							});
						}
					}).exec();
				});
			},

			// 下载报告
			async downloadReport() {
				try {
					uni.showLoading({
						title: '正在生成图片...',
						mask: true
					});

					// 获取wxml-to-canvas组件实例
					this.canvasWidget = this.selectComponent('.canvas-widget');
					if (!this.canvasWidget) {
						throw new Error('canvas组件未找到');
					}

					// 生成wxml模板和样式
					const {wxml, style} = this.generateCanvasContent();

					// 渲染到canvas
					await this.canvasWidget.renderToCanvas({wxml, style});

					// 转换为临时文件
					const result = await this.canvasWidget.canvasToTempFilePath({
						fileType: 'png',
						quality: 1
					});

					uni.hideLoading();

					// 保存到相册
					await this.saveImageToAlbum(result.tempFilePath);

				} catch (error) {
					uni.hideLoading();
					console.error('下载报告失败:', error);
					uni.showToast({
						title: '生成图片失败',
						icon: 'none'
					});
				}
			},

			// 生成canvas内容
			generateCanvasContent() {
				// 获取当前显示的内容
				let content = '';
				if (this.type === 3) {
					content = this.streamData;
				} else if (this.type === 1) {
					content = this.steps[0].content;
				} else if (this.type === 2) {
					content = this.steps[this.currentStep].content;
				} else {
					content = this.streamData;
				}

				// 处理markdown内容，转换为纯文本
				const plainText = this.markdownToPlainText(content);

				// 分割文本为多行
				const lines = this.splitTextToLines(plainText, 28); // 每行最多28个字符

				// 生成wxml模板
				const wxml = `
					<view class="report-container">
						<view class="report-header">
							<text class="report-title">${this.getReportTitle()}</text>
						</view>
						<view class="report-content">
							${lines.map(line => `<text class="content-line">${line}</text>`).join('')}
						</view>
						<view class="report-footer">
							<text class="footer-text">由AI智能生成</text>
						</view>
					</view>
				`;

				// 样式配置
				const style = {
					reportContainer: {
						width: 750,
						height: Math.max(1000, lines.length * 40 + 200), // 根据内容动态调整高度
						backgroundColor: '#111D37',
						padding: 30,
						flexDirection: 'column'
					},
					reportHeader: {
						width: 690,
						height: 80,
						marginBottom: 30,
						justifyContent: 'center',
						alignItems: 'center'
					},
					reportTitle: {
						width: 690,
						height: 80,
						fontSize: 32,
						color: '#FFFFFF',
						textAlign: 'center',
						verticalAlign: 'middle',
						fontWeight: 'bold'
					},
					reportContent: {
						width: 690,
						flexDirection: 'column',
						marginBottom: 30
					},
					contentLine: {
						width: 690,
						height: 40,
						fontSize: 24,
						color: '#E5E5E5',
						lineHeight: '40px',
						marginBottom: 5
					},
					reportFooter: {
						width: 690,
						height: 60,
						justifyContent: 'center',
						alignItems: 'center'
					},
					footerText: {
						width: 690,
						height: 60,
						fontSize: 20,
						color: '#999999',
						textAlign: 'center',
						verticalAlign: 'middle'
					}
				};

				return {wxml, style};
			},

			// 获取报告标题
			getReportTitle() {
				if (this.type === 3) {
					return '目标人群分析报告';
				} else if (this.type === 1) {
					return 'AI行业诊断报告';
				} else if (this.type === 2) {
					const titles = ['行业诊断报告', '详细运营方案', 'AI企业运营计划督导'];
					return titles[this.currentStep] || 'AI商业定位报告';
				} else if (this.type === 4) {
					return '热点话题分析';
				} else if (this.type === 5) {
					return '会议报告';
				}
				return 'AI分析报告';
			},

			// markdown转纯文本
			markdownToPlainText(markdown) {
				if (!markdown) return '';

				// 移除markdown语法
				let text = markdown
					.replace(/#{1,6}\s+/g, '') // 移除标题标记
					.replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记
					.replace(/\*(.*?)\*/g, '$1') // 移除斜体标记
					.replace(/`(.*?)`/g, '$1') // 移除代码标记
					.replace(/\[(.*?)\]\(.*?\)/g, '$1') // 移除链接，保留文本
					.replace(/!\[.*?\]\(.*?\)/g, '') // 移除图片
					.replace(/>\s+/g, '') // 移除引用标记
					.replace(/^\s*[-*+]\s+/gm, '') // 移除列表标记
					.replace(/^\s*\d+\.\s+/gm, '') // 移除有序列表标记
					.replace(/\n{3,}/g, '\n\n') // 合并多个换行
					.trim();

				return text;
			},

			// 分割文本为多行
			splitTextToLines(text, maxCharsPerLine) {
				const lines = [];
				const paragraphs = text.split('\n');

				paragraphs.forEach(paragraph => {
					if (paragraph.trim() === '') {
						lines.push(''); // 保留空行
						return;
					}

					// 按字符数分割段落
					let currentLine = '';
					for (let i = 0; i < paragraph.length; i++) {
						currentLine += paragraph[i];
						if (currentLine.length >= maxCharsPerLine) {
							lines.push(currentLine);
							currentLine = '';
						}
					}
					if (currentLine) {
						lines.push(currentLine);
					}
				});

				return lines;
			},

			// 保存图片到相册
			async saveImageToAlbum(filePath) {
				return new Promise((resolve, reject) => {
					uni.saveImageToPhotosAlbum({
						filePath: filePath,
						success: (res) => {
							uni.showToast({
								title: '报告已保存到相册',
								icon: 'success'
							});
							resolve(res);
						},
						fail: (err) => {
							console.error('保存图片失败:', err);
							// 处理权限问题
							if (err.errMsg && err.errMsg.indexOf('auth deny') >= 0) {
								uni.showModal({
									title: '需要相册权限',
									content: '保存图片需要访问您的相册，请在设置中开启权限',
									confirmText: '去设置',
									success: (modalRes) => {
										if (modalRes.confirm) {
											uni.openSetting({
												success: (settingRes) => {
													console.log('设置页面返回:', settingRes);
												},
												fail: (settingErr) => {
													console.error('打开设置失败:', settingErr);
												}
											});
										}
									}
								});
							} else {
								uni.showToast({
									title: '保存失败',
									icon: 'none'
								});
							}
							reject(err);
						}
					});
				});
			},
		},
		onLoad(options) {
			if (options.type) {
				this.type = Number(options.type);
			}
			if (this.type === 3) {
				uni.setNavigationBarTitle({
					title: '目标人群'
				})
			} else if (this.type === 4) {
				uni.setNavigationBarTitle({
					title: '热点话题'
				})
			} else if (this.type === 5) {
				uni.setNavigationBarTitle({
					title: '会议报告'
				})
				this.meeting_id = options.meeting_id;
				this.getMeetingReport();
			} else {
				uni.setNavigationBarTitle({
					title: 'AI诊断'
				})
			}
			if ([1, 2, 3].includes(this.type)) {
				this.isLoading = true;
				this.getReport();
			}
			if (this.type === 2) {
				uni.setNavigationBarTitle({
					title: 'AI商业定位'
				})
				// this.userData = uni.getStorageSync('userData');
				// this.startStream(0);
			} else if (this.type === 4) {
				// 热点话题
				this.startStream();
			}
		},
		onUnload() {
			this.stopStream();
		}
	}
</script>

<style lang="scss">
	page {
		border-top: none;
		background-color: #080E1E;
		overflow-x: hidden;
	}

	.button-group {
		margin-top: 80rpx;
		display: flex;
		justify-content: space-between;
		gap: 20rpx;
	}

	.m-but {
		font-size: 32rpx;
		color: #FFF;
		padding: 30rpx 0;
		flex: 1;
		text-align: center;
		border-radius: 10rpx;
		box-shadow: 0px -1px 11px 4px rgba(127, 27, 255, 0.2);
		background: linear-gradient(96.34deg, rgb(210, 174, 255), rgb(154, 93, 228));
	}

	.download-but {
		background: linear-gradient(96.34deg, rgb(174, 255, 210), rgb(93, 228, 154));
		box-shadow: 0px -1px 11px 4px rgba(27, 255, 127, 0.2);
	}

	.r-top {
		padding: 30rpx 20rpx;
	}

	.step-indicator {
		display: flex;
		justify-content: space-between;
		margin-bottom: 40rpx;
		padding: 30rpx;
		background-color: #111D37;
		border-radius: 12rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
		position: relative;
	}

	.step-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		flex: 1;
		z-index: 1;
	}

	.step-circle {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		background: #2A324A;
		display: flex;
		align-items: center;
		justify-content: center;
		color: rgba(255, 255, 255, 0.7);
		font-weight: bold;
		margin-bottom: 10rpx;
		transition: all 0.3s ease;
	}

	.step-item.active .step-circle,
	.step-item.completed .step-circle {
		background: linear-gradient(96.34deg, rgb(210, 174, 255), rgb(154, 93, 228));
		box-shadow: 0 0 12rpx rgba(154, 93, 228, 0.6);
	}

	.step-title {
		font-size: 28rpx;
		font-weight: bold;
		margin-bottom: 6rpx;
		text-align: center;
		color: rgba(255, 255, 255, 0.85);
	}

	.step-status {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.5);
	}

	.step-item.active .step-status {
		color: rgb(210, 174, 255);
		font-weight: 500;
	}

	.step-item.completed .step-status {
		color: #5fe6ae;
	}

	.step-line {
		position: absolute;
		top: 30rpx;
		right: -50%;
		width: 100%;
		height: 4rpx;
		background-color: #2A324A;
		z-index: -1;
	}

	.step-item.completed .step-line {
		background: linear-gradient(96.34deg, rgb(210, 174, 255), rgb(154, 93, 228));
	}

	.report-content {
		background-color: #111D37;
		border-radius: 12rpx;
		padding-bottom: 20rpx;
	}
</style>