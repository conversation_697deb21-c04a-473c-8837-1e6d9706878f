<template>
	<view>
		<!-- <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
			<image class="avatar" :src="user.avatar?user.avatar: imgUrl+'33.png'"></image>
		</button>
		<view class="user">点击获取/更新用户头像</view>
		<view class="input-view display-flex">
			<label>用户昵称</label>
			<view>
				<input type="nickname" style="width: 500rpx;" placeholder="请输入昵称或者直接获取微信昵称" v-model="user.nickname"
					@blur="onBlru" placeholder-class="font-size_28rpx" />
			</view>
		</view>
		<view class="input-view display-flex">
			<label>手机号码</label>
			<view v-if="user.telphone">
				<input type="number" maxlength="11" style="width: 500rpx;" placeholder="请输入联系电话" v-model="user.telphone"
					 placeholder-class="font-size_28rpx" />
			</view>
			<view v-else>
				<button class="button-phone" open-type="getPhoneNumber" @getphonenumber="decryptPhoneNumber">获取手机号码</button>
			</view>
		</view>
		<view class="display-flex input-view" v-if="note_open == 1">
			<label>验证码</label>
			<input style="width: 300rpx;" type="number" v-model="input_code" placeholder="请输入验证码"
				placeholder-class="placeholder" />
			<view class="requst-code" @click="getCode">
				<text class="verific-text">{{title}}</text>
			</view>
			
		</view>
		<block v-if="type == 1">
			<view class="pick-button" @click="auth()">确认提交</view>
			<view class="cancel" @click="close()">取消</view>
		</block>
		<block v-if="type == 2">
			<view class="pick-button" @click="sub()">确认更改</view>
		</block> -->
		<view class="display-ac-jc" style="padding-top: 260rpx;">
			<image class="img-logo" :src="systems.logo"></image>
			<view class="a-name">{{systems.name}}</view>
			<view style="color: #B8B8B8;font-size: 26rpx;">在线智能视频创作平台</view>
			<view class="a-bott">
				<button class="button-phone" open-type="getPhoneNumber"
					@getphonenumber="decryptPhoneNumber">一键手机号登录/注册</button>
				<view @click="navig()" class="text-align_center font-size_26rpx" style="color: #B8B8B8;">取消登录</view>
			</view>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				user: {
					id: '',
					avatar: '',
					nickname: '',
					telphone: '',
				},

				imgUrl: this.$imgUrl,

				type: '', //1用户授权  2修改用户信息

				flag: false,

				note_open: '', //1开启

				input_code: '',
				code: '',
				title: '获取验证码',
				timer: 60,
				disabled: false,

				systems: {},
			}
		},

		// onUnload() { //监听是否跳转页面
		// 	if (this.flag) {
		// 		//tjzt为变量
		// 	} else {
		// 		uni.navigateBack({
		// 			delta: 2
		// 		})
		// 	}

		// },

		onLoad(options) {
			this.login();
			// if (options.type) {
			// 	this.type = options.type;
			// 	if (options.type == 1) {
			// 		this.login();
			// 		this.$sun.title("完善信息");
			// 	}
			// 	if (options.type == 2) {
			// 		this.userInfo();
			// 		this.$sun.title("更改信息");
			// 	}
			// }
		},

		onShow() {
			// this.sendSet();
			this.getSystem();
		},

		methods: {

			//系统设置
			async getSystem() {
				const result = await this.$http.post({
					url: this.$api.system
				});
				if (result.errno == 0) {
					this.systems = result.data;
					uni.setStorageSync('system', result.data);
				}
			},

			//短信设置
			async sendSet() {
				const result = await this.$http.post({
					url: this.$api.noteConfig,
				});
				if (result.errno == 0) {
					this.note_open = result.data.note_open;
				} else {
					this.$sun.toast(result.message, 'none');
				}
			},

			//获取验证码
			getCode() {
				let flag = true;
				if (this.disabled) {
					console.log('重复点击');
				} else {
					if (!(/^1[3456789]\d{9}$/.test(this.user.telphone))) {
						uni.showToast({
							title: '请输入正确的手机号码',
							icon: 'none'
						});
						flag = false;
						return;
					} else {
						this.disabled = true;
						let timer1 = setInterval(() => {
							this.timer--;
							this.title = this.timer + 's后重新发送';
							if (this.timer == 0) {
								clearInterval(timer1);
								this.title = '获取验证码';
								this.disabled = false;
								this.timer = 60;
								return;
							}
						}, 1000);
					}
				}
				if (flag) {
					this.sendMobile();
				}
			},

			async sendMobile() {
				const result = await this.$http.post({
					url: this.$api.sendMobile,
					data: {
						telphone: this.user.telphone
					}
				});
				if (result.errno == 0) {
					this.code = result.data;
				}
			},

			//获取手机号码
			decryptPhoneNumber(e) {
				this.getPhone(e.detail.code);
			},

			//获取手机号码
			async getPhone(code) {
				const result = await this.$http.post({
					url: this.$api.getTelphone,
					data: {
						code: code
					}
				});
				if (result.errno == 0) {
					if (result.data.errcode == 0) {
						this.user.telphone = result.data.phone_info.phoneNumber;
						this.auth();
					} else {
						this.$sun.toast(result.data.errmsg, 'none');
					}
				} else {
					this.$sun.toast(result.message, 'none');
				}
			},

			//用户信息
			async userInfo() {
				const result = await this.$http.post({
					url: this.$api.userInfo,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					this.user = result.data;
				}
			},

			login() {
				uni.login({
					provider: 'weixin',
					success: async res => {
						const result = await this.$http.post({
							url: this.$api.getOpenId,
							data: {
								code: res.code
							}
						});
						if (result.errno == 0) {
							uni.setStorageSync('openid', result.data.openid);
							uni.setStorageSync('session_key', result.data.session_key);
						} else {
							this.$sun.toast(result.message, 'none');
						}
					}
				});
			},

			onBlru(e) {
				this.user.nickname = e.detail.value;
			},

			onChooseAvatar(e) {
				// this.user.avatar = e.detail.avatarUrl;
				uni.uploadFile({
					url: this.$api.upload, //后台接口
					filePath: e.detail.avatarUrl, // 上传图片 url
					name: 'file',
					success: res => {
						res = JSON.parse(res.data);

						// console.log('uploadRes', res);

						if (res.errno != 0) {
							uni.showToast({
								title: '上传失败 : ' + res.data,
								icon: 'none'
							});
						} else {
							//上传图片成功
							this.user.avatar = res.data;
						}
					},
					fail: e => {
						this.$toast('上传失败')
					}
				});
			},

			close() {
				uni.navigateBack({
					delta: 2
				})
			},

			//登录授权
			auth() {

				// if (!this.user.avatar) {
				// 	this.$sun.toast("请上传头像", "error");
				// 	return;
				// }
				// if (!this.user.nickname) {
				// 	this.$sun.toast("请输入名称", "error");
				// 	return;
				// }

				if (!this.user.telphone) {
					this.$sun.toast("请获取手机号码", "error");
					return;
				}

				// if (this.note_open == 1) {
				// 	if (!this.input_code) {
				// 		this.$sun.toast("请输入验证码", "none");
				// 		return;
				// 	} else {
				// 		if (this.code != this.input_code) {
				// 			this.$sun.toast("验证码错误", "error");
				// 			return;
				// 		}
				// 	}
				// }

				uni.request({
					url: this.$api.register,
					method: "POST",
					header: {
						"Content-type": "application/json"
					},
					data: {
						pid: uni.getStorageSync('pid'),
						openid: uni.getStorageSync('openid'),
						// avatar: this.user.avatar,
						// nickname: this.user.nickname,
						telphone: this.user.telphone
					},
					success: res => {
						// console.log('成功', res);
						if (res.errMsg == "request:ok") {
							uni.setStorageSync("uid", res.data.data);
							uni.showToast({
								title: '登录成功',
								duration: 1500,
							});
							setTimeout(() => {
								this.flag = true;
								this.navig();
							}, 1500);
						}
					},
					fail: (err) => {
						// console.log('失败', err);
					},
					complete: (res) => {
						// console.log('完成', res);
						// uni.setStorageSync('userInfo', userDetailInfo);
					}
				});
			},

			/*  更新  */
			async sub() {

				let flag = true;
				if (!this.user.avatar) {
					this.$sun.toast("请上传头像", "error");
					flag = false;
					return;
				}
				if (!this.user.nickname) {
					this.$sun.toast("请输入名称", "error");
					flag = false;
					return;
				}

				if (!this.user.telphone) {
					this.$sun.toast("请获取手机号码", "error");
					return;
				}

				if (this.note_open == 1) {
					if (!this.input_code) {
						this.$sun.toast("请输入验证码", "none");
						return;
					} else {
						if (this.code != this.input_code) {
							this.$sun.toast("验证码错误", "error");
							return;
						}
					}
				}

				if (flag) {

					const result = await this.$http.post({
						url: this.$api.register,
						data: {
							openid: uni.getStorageSync('openid'),
							nickname: this.user.nickname,
							avatar: this.user.avatar,
							telphone: this.user.telphone
						}
					});
					if (result.errno == 0) {
						uni.showToast({
							title: '更新成功',
							duration: 1500,
						});
						setTimeout(() => {
							this.navig();
						}, 1500);
					} else {
						this.$sun.toast(result.message, "none");
					}
				}
			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

		}
	}
</script>

<style lang="scss">
	.a-bott {
		position: fixed;
		bottom: 100rpx;
		width: 750rpx;
		background-color: #111317;
		z-index: 2;
	}

	.a-name {
		font-weight: bold;
		font-size: 40rpx;
		color: #FFF;
		margin-bottom: 20rpx;
	}

	.img-logo {
		width: 136rpx;
		height: 136rpx;
		border-radius: 100rpx;
		margin-bottom: 10rpx;
	}

	.requst-code {
		width: 200rpx;
		text-align: center;
		background-color: #17B10A;
		border-radius: 100rpx;
		padding: 10rpx;
		margin-left: auto;
	}

	.verific-text {
		font-size: 24rpx;
		color: #FFFFFF;
	}

	.button-phone {
		width: 610rpx;
		text-align: center;
		color: #FFFFFF;
		background-color: #17B10A;
		border-radius: 100rpx;
		padding: 30rpx 0;
		font-size: 26rpx;
		line-height: 1;
		margin: 0 70rpx 20rpx;
	}

	.bott {
		position: fixed;
		bottom: 0;
		width: 750rpx;
	}

	.user {
		text-align: center;
		color: #999999;
		font-size: 26rpx;
		margin-bottom: 60rpx;
	}

	page {
		background-color: #111317;
		border: none;
	}

	.avatar-wrapper {
		background-color: #FFFFFF;
		padding: 80rpx 0 20rpx;
	}

	.avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
	}

	.input-view {
		align-items: center;
		padding: 30rpx;
		border-bottom: 2rpx solid #F7F7F7;

		>label {
			width: 168rpx;
			text-align: center;
		}

	}

	.cancel {
		width: 360rpx;
		background-color: #f7f7f7;
		color: #E53A3A;
		border-radius: 10rpx;
		text-align: center;
		font-size: 30rpx;
		padding: 20rpx;
		margin: 0 195rpx;
	}

	.pick-button {
		width: 360rpx;
		background-color: #17B10A;
		color: #ffffff;
		border-radius: 10rpx;
		text-align: center;
		font-size: 30rpx;
		padding: 20rpx;
		margin: 120rpx 195rpx 30rpx;
	}
</style>